<?xml version="1.0" encoding="utf-8"?>
<CheatTable CheatEngineTableVersion="18">
  <CheatEntries>
    <CheatEntry>
      <ID>3245</ID>
      <Description>"ATTRIBUTES &amp; UPGRADES (CLICK TABLE EXTRAS IN THE BOTTOM RIGHT)"</Description>
      <Options moHideChildren="1"/>
      <LastState Value="" Activated="0" RealAddress="00000000"/>
      <Color>80000008</Color>
      <GroupHeader>1</GroupHeader>
      <CheatEntries>
        <CheatEntry>
          <ID>4050</ID>
          <Description>"ATTRIBUTES"</Description>
          <Options moHideChildren="1"/>
          <LastState Value="" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <GroupHeader>1</GroupHeader>
          <CheatEntries>
            <CheatEntry>
              <ID>3458</ID>
              <Description>"ADDRESSES FOR MY DESKTOP"</Description>
              <LastState Value="" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <GroupHeader>1</GroupHeader>
            </CheatEntry>
            <CheatEntry>
              <ID>4051</ID>
              <Description>"Standing Layup"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE28</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4052</ID>
              <Description>"Driving Layup"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE29</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4053</ID>
              <Description>"Post Fadeaway"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE2A</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4054</ID>
              <Description>"Post Hook"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE2B</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4055</ID>
              <Description>"Post Control"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE2C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4056</ID>
              <Description>"Moving Shot Close"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE2D</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4057</ID>
              <Description>"Standing Shot Close"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE2E</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4058</ID>
              <Description>"Moving Shot Mid-Range"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE2F</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4059</ID>
              <Description>"Standing Shot Mid-Range"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE30</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4060</ID>
              <Description>"Moving Shot Three"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE31</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4061</ID>
              <Description>"Standing Shot Three"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE32</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4062</ID>
              <Description>"Free Throw"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE33</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4063</ID>
              <Description>"Ball Control"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE34</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4064</ID>
              <Description>"Passing Vision"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE35</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4065</ID>
              <Description>"Passing IQ"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE36</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4066</ID>
              <Description>"Passing Accuracy"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE37</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4067</ID>
              <Description>"Boxout"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE38</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4068</ID>
              <Description>"Offensive Rebound"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE39</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4069</ID>
              <Description>"Defensive Rebound"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE3A</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4070</ID>
              <Description>"Lateral Quickness"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE3B</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4071</ID>
              <Description>"Pass Perception"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE3C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4072</ID>
              <Description>"Block"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE3D</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4073</ID>
              <Description>"Shot Contest"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE3E</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4074</ID>
              <Description>"Steal"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE3F</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4075</ID>
              <Description>"Defensive Consitency"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE40</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4076</ID>
              <Description>"On-Ball IQ"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE41</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4077</ID>
              <Description>"Pick and Roll Defense IQ"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE42</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4078</ID>
              <Description>"Help Defensive IQ"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE43</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4079</ID>
              <Description>"Low Post Defense IQ"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE44</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4080</ID>
              <Description>"Standing Dunk"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE45</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4081</ID>
              <Description>"Driving Dunk"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE46</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4082</ID>
              <Description>"Speed"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE47</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4083</ID>
              <Description>"Quickness"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE48</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4084</ID>
              <Description>"Vertical"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE49</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4085</ID>
              <Description>"Strength"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE4A</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4086</ID>
              <Description>"Stamina"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE4B</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4087</ID>
              <Description>"Misc Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE4C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4088</ID>
              <Description>"Hustle"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE4D</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4089</ID>
              <Description>"Shot IQ"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE4E</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4090</ID>
              <Description>"Hands"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE4F</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4091</ID>
              <Description>"Offensive Consitency"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE50</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4092</ID>
              <Description>"Potential"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE51</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4093</ID>
              <Description>"R Foot Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE60</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4094</ID>
              <Description>"L Foot Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE5F</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4095</ID>
              <Description>"R Ankle Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE5E</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4096</ID>
              <Description>"L Ankle Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE5D</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4097</ID>
              <Description>"R Knee Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE5C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4098</ID>
              <Description>"L Knee Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE5B</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4099</ID>
              <Description>"R Lip Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE5A</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4100</ID>
              <Description>"L Hip Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE59</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4101</ID>
              <Description>"R Elbow Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE58</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4102</ID>
              <Description>"L Elbow Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE57</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4103</ID>
              <Description>"R Shoulder Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE56</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4104</ID>
              <Description>"L Shoulder Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE55</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4105</ID>
              <Description>"Back Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE54</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4106</ID>
              <Description>"Neck Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE53</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>4107</ID>
              <Description>"Head Durability"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+37BAE52</Address>
            </CheatEntry>
          </CheatEntries>
        </CheatEntry>
        <CheatEntry>
          <ID>4196</ID>
          <Description>"BADGES"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <ShowAsHex>1</ShowAsHex>
          <Color>80000008</Color>
          <VariableType>Array of byte</VariableType>
          <ByteLength>16</ByteLength>
          <Address>nba2k15.exe+37BBE48+88</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>7</ID>
          <Description>"FAKE VC - LOCK IN"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>4 Bytes</VariableType>
          <Address>nba2k15.exe+249DAF0</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>5</ID>
          <Description>"UPGRADE COST - LOCK BOTH AT 0"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>4 Bytes</VariableType>
          <Address>nba2k15.exe+246713C</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>6</ID>
          <Description>"UPGRADE COST - LOCK BOTH AT 0"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>4 Bytes</VariableType>
          <Address>nba2k15.exe+2467140</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>8</ID>
          <Description>"UPGRADES AVAILABLE"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>4 Bytes</VariableType>
          <Address>nba2k15.exe+32B8054</Address>
        </CheatEntry>
      </CheatEntries>
    </CheatEntry>
    <CheatEntry>
      <ID>3896</ID>
      <Description>"GAME MODE OPTIONS"</Description>
      <Options moHideChildren="1"/>
      <LastState Value="" Activated="0" RealAddress="00000000"/>
      <Color>80000008</Color>
      <GroupHeader>1</GroupHeader>
      <CheatEntries>
        <CheatEntry>
          <ID>3897</ID>
          <Description>"Conference Quarterfinals"</Description>
          <DropDownList ReadOnly="1" DescriptionOnly="1" DisplayValueAsItem="1">0:Best of 7
1:Best of 1
2:Best of 3
3:Best of 5
</DropDownList>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>2 Bytes</VariableType>
          <Address>nba2k15.exe+32B1FFC</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3898</ID>
          <Description>"Conference Semifinals"</Description>
          <DropDownList ReadOnly="1" DescriptionOnly="1" DisplayValueAsItem="1">0:Best of 7
1:Best of 1
2:Best of 3
3:Best of 5
</DropDownList>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>2 Bytes</VariableType>
          <Address>nba2k15.exe+32B2000</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3899</ID>
          <Description>"Conference Finals"</Description>
          <DropDownList ReadOnly="1" DescriptionOnly="1" DisplayValueAsItem="1">0:Best of 7
1:Best of 1
2:Best of 3
3:Best of 5
</DropDownList>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>2 Bytes</VariableType>
          <Address>nba2k15.exe+32B2004</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3900</ID>
          <Description>"NBA Finals"</Description>
          <DropDownList ReadOnly="1" DescriptionOnly="1" DisplayValueAsItem="1">0:Best of 7
1:Best of 1
2:Best of 3
3:Best of 5
</DropDownList>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>2 Bytes</VariableType>
          <Address>nba2k15.exe+32B2008</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3901</ID>
          <Description>"Season Length"</Description>
          <DropDownList ReadOnly="1" DescriptionOnly="1" DisplayValueAsItem="1">0:14
1:28
2:52
3:82
</DropDownList>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>4 Bytes</VariableType>
          <Address>nba2k15.exe+32B2010</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3902</ID>
          <Description>"Simulator Difficulty [ 0-  100 ]"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>4 Bytes</VariableType>
          <Address>nba2k15.exe+32B20A4</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3903</ID>
          <Description>"User Injury Freq [ 0 - 100 ]"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>4 Bytes</VariableType>
          <Address>nba2k15.exe+32B20A8</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3904</ID>
          <Description>"CPU Injury Freq [ 0 - 100 ]"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>4 Bytes</VariableType>
          <Address>nba2k15.exe+32B20AC</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3905</ID>
          <Description>"Normalize Played to Sim Minutes"</Description>
          <DropDownList ReadOnly="1" DescriptionOnly="1" DisplayValueAsItem="1">1:On
0:Off
</DropDownList>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>4 Bytes</VariableType>
          <Address>nba2k15.exe+32B20CC</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3906</ID>
          <Description>"Game Style"</Description>
          <DropDownList ReadOnly="1" DescriptionOnly="1" DisplayValueAsItem="1">0:Default
1:Simulation
2:Casual
</DropDownList>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>4 Bytes</VariableType>
          <Address>nba2k15.exe+32B20F8</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3907</ID>
          <Description>"Difficulty"</Description>
          <DropDownList ReadOnly="1" DescriptionOnly="1" DisplayValueAsItem="1">0:Rookie
1:Pro
2:All Star
3Superstar
4:Hall of Fame
</DropDownList>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>4 Bytes</VariableType>
          <Address>nba2k15.exe+32C9440</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3908</ID>
          <Description>"Game Speed [ 0 - 1 ]"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>Float</VariableType>
          <Address>nba2k15.exe+32C9444</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3909</ID>
          <Description>"Quarter Lenghth [ 1 - 12 ]"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>4 Bytes</VariableType>
          <Address>nba2k15.exe+32C945C</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3910</ID>
          <Description>"Injuries"</Description>
          <DropDownList ReadOnly="1" DescriptionOnly="1" DisplayValueAsItem="1">1:On
0:Off
</DropDownList>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>8 Bytes</VariableType>
          <Address>nba2k15.exe+32C9460</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3911</ID>
          <Description>"FT Shooting Type"</Description>
          <DropDownList ReadOnly="1" DescriptionOnly="1" DisplayValueAsItem="1">0:User timing
1:Real %
</DropDownList>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>2 Bytes</VariableType>
          <Address>nba2k15.exe+32C94AC</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3912</ID>
          <Description>"Free Throw Difficulty [ 0 - 1 by 0.5 ]"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>Float</VariableType>
          <Address>nba2k15.exe+32C94B0</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3913</ID>
          <Description>"Shooting Type"</Description>
          <DropDownList ReadOnly="1" DescriptionOnly="1" DisplayValueAsItem="1">0:User timing
1:Real %
</DropDownList>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>2 Bytes</VariableType>
          <Address>nba2k15.exe+32C94B4</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3914</ID>
          <Description>"Fatigue"</Description>
          <DropDownList ReadOnly="1" DescriptionOnly="1" DisplayValueAsItem="1">1:On
0:Off
</DropDownList>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>8 Bytes</VariableType>
          <Address>nba2k15.exe+3C79A78</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3915</ID>
          <Description>"Sim Quarter Length [ 1 - 12 ]"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>2 Bytes</VariableType>
          <Address>nba2k15.exe+32B1FF8</Address>
        </CheatEntry>
      </CheatEntries>
    </CheatEntry>
    <CheatEntry>
      <ID>3411</ID>
      <Description>"MATCH"</Description>
      <Options moHideChildren="1"/>
      <LastState Value="" Activated="0" RealAddress="00000000"/>
      <Color>80000008</Color>
      <GroupHeader>1</GroupHeader>
      <CheatEntries>
        <CheatEntry>
          <ID>3453</ID>
          <Description>"TEAMMATE GRADE"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>Float</VariableType>
          <Address>nba2k15.exe+2A70E80</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>362</ID>
          <Description>"QUARTER"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>Byte</VariableType>
          <Address>nba2k15.exe+2A6F1DC</Address>
          <Hotkeys>
            <Hotkey>
              <Action>Set Value</Action>
              <Keys>
                <Key>79</Key>
              </Keys>
              <Value>4</Value>
              <ID>0</ID>
            </Hotkey>
          </Hotkeys>
        </CheatEntry>
        <CheatEntry>
          <ID>359</ID>
          <Description>"QUARTER CLOCK"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <VariableType>Double</VariableType>
          <Address>nba2k15.exe+2A6F240</Address>
          <Hotkeys>
            <Hotkey>
              <Action>Set Value</Action>
              <Keys>
                <Key>80</Key>
              </Keys>
              <Value>0</Value>
              <ID>0</ID>
            </Hotkey>
          </Hotkeys>
        </CheatEntry>
        <CheatEntry>
          <ID>3384</ID>
          <Description>"USER SLIDERS"</Description>
          <Options moHideChildren="1"/>
          <LastState Value="" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <GroupHeader>1</GroupHeader>
          <CheatEntries>
            <CheatEntry>
              <ID>261</ID>
              <Description>"Alley-Oop Success"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1214</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>262</ID>
              <Description>"Contact Shot Success"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A121C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>263</ID>
              <Description>"Inside Shot Succes"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1224</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>264</ID>
              <Description>"Close Shot Success"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A122C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>265</ID>
              <Description>"Mid-Range Success"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1234</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>266</ID>
              <Description>"3PT Success"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A123C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>5001</ID>
              <Description>"FULL COURT SHOT MODS"</Description>
              <Options moHideChildren="1"/>
              <LastState Value="" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <GroupHeader>1</GroupHeader>
              <CheatEntries>
                <CheatEntry>
                  <ID>5002</ID>
                  <Description>"Shot Distance Penalty Multiplier"</Description>
                  <LastState Value="0" Activated="0" RealAddress="00000000"/>
                  <Color>00FF00</Color>
                  <VariableType>Float</VariableType>
                  <Address>nba2k15.exe+32A1300</Address>
                </CheatEntry>
                <CheatEntry>
                  <ID>5003</ID>
                  <Description>"Max Shot Range Override"</Description>
                  <LastState Value="999" Activated="0" RealAddress="00000000"/>
                  <Color>00FF00</Color>
                  <VariableType>Float</VariableType>
                  <Address>nba2k15.exe+32A1304</Address>
                </CheatEntry>
                <CheatEntry>
                  <ID>5004</ID>
                  <Description>"Half Court Shot Success"</Description>
                  <LastState Value="1" Activated="0" RealAddress="00000000"/>
                  <Color>00FF00</Color>
                  <VariableType>Float</VariableType>
                  <Address>nba2k15.exe+32A1308</Address>
                </CheatEntry>
                <CheatEntry>
                  <ID>5005</ID>
                  <Description>"Full Court Shot Success"</Description>
                  <LastState Value="1" Activated="0" RealAddress="00000000"/>
                  <Color>00FF00</Color>
                  <VariableType>Float</VariableType>
                  <Address>nba2k15.exe+32A130C</Address>
                </CheatEntry>
                <CheatEntry>
                  <ID>5006</ID>
                  <Description>"Shot Type Detection Override"</Description>
                  <LastState Value="1" Activated="0" RealAddress="00000000"/>
                  <Color>00FF00</Color>
                  <VariableType>Byte</VariableType>
                  <Address>nba2k15.exe+32A1310</Address>
                </CheatEntry>
              </CheatEntries>
            </CheatEntry>
            <CheatEntry>
              <ID>267</ID>
              <Description>"Layup Success"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1244</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>268</ID>
              <Description>"Dunk in Traffic Freq"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A124C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>269</ID>
              <Description>"Dunk in Tra Success"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1254</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>271</ID>
              <Description>"Pass Accuracy"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1264</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>272</ID>
              <Description>"Steal Success"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A126C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>273</ID>
              <Description>"Driving Con Shot Freq"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1274</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>274</ID>
              <Description>"Inside Con Shot Freq"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A127C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>275</ID>
              <Description>"Layup Def Str (Takeoff)"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1284</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>276</ID>
              <Description>"Layup Def Str (Release)"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A128C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>277</ID>
              <Description>"Jump Shot Def Str (Gather)"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1294</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>278</ID>
              <Description>"Jump Shot Def Str (Release)"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A129C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>292</ID>
              <Description>"Help Defense Str"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1334</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>5007</ID>
              <Description>"ADVANCED SHOT MECHANICS"</Description>
              <Options moHideChildren="1"/>
              <LastState Value="" Activated="0" RealAddress="00000000"/>
              <Color>80000008</Color>
              <GroupHeader>1</GroupHeader>
              <CheatEntries>
                <CheatEntry>
                  <ID>5008</ID>
                  <Description>"Shot Arc Calculation Override"</Description>
                  <LastState Value="0" Activated="0" RealAddress="00000000"/>
                  <Color>FF00FF</Color>
                  <VariableType>Float</VariableType>
                  <Address>nba2k15.exe+32A1338</Address>
                </CheatEntry>
                <CheatEntry>
                  <ID>5009</ID>
                  <Description>"Shot Power Multiplier"</Description>
                  <LastState Value="2" Activated="0" RealAddress="00000000"/>
                  <Color>FF00FF</Color>
                  <VariableType>Float</VariableType>
                  <Address>nba2k15.exe+32A133C</Address>
                </CheatEntry>
                <CheatEntry>
                  <ID>5010</ID>
                  <Description>"Distance Fade Penalty Disable"</Description>
                  <LastState Value="1" Activated="0" RealAddress="00000000"/>
                  <Color>FF00FF</Color>
                  <VariableType>Byte</VariableType>
                  <Address>nba2k15.exe+32A1340</Address>
                </CheatEntry>
                <CheatEntry>
                  <ID>5011</ID>
                  <Description>"Extreme Range Shot Enable"</Description>
                  <LastState Value="1" Activated="0" RealAddress="00000000"/>
                  <Color>FF00FF</Color>
                  <VariableType>Byte</VariableType>
                  <Address>nba2k15.exe+32A1341</Address>
                </CheatEntry>
              </CheatEntries>
            </CheatEntry>
          </CheatEntries>
        </CheatEntry>
        <CheatEntry>
          <ID>3412</ID>
          <Description>"STATISTICS (UNSTABLE MAY CRASH GAME)"</Description>
          <Options moHideChildren="1"/>
          <LastState Value="" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <GroupHeader>1</GroupHeader>
          <CheatEntries>
            <CheatEntry>
              <ID>3413</ID>
              <Description>"POINTS"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>4080FF</Color>
              <VariableType>4 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>628</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3414</ID>
              <Description>"FT MADE"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>808000</Color>
              <VariableType>2 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>62C</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3415</ID>
              <Description>"FT ATTEMPTED"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>4080FF</Color>
              <VariableType>2 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>62E</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3416</ID>
              <Description>"2PT MADE"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>808000</Color>
              <VariableType>2 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>630</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3417</ID>
              <Description>"2PT ATTEMPTED"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>4080FF</Color>
              <VariableType>2 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>632</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3418</ID>
              <Description>"3PT MADE"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>808000</Color>
              <VariableType>2 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>634</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3419</ID>
              <Description>"3PT ATTEMPTED"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>4080FF</Color>
              <VariableType>2 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>636</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3420</ID>
              <Description>"DUNKS"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>808000</Color>
              <VariableType>2 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>658</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3421</ID>
              <Description>"O REBS"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>4080FF</Color>
              <VariableType>2 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>838</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3422</ID>
              <Description>"D REBS"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>808000</Color>
              <VariableType>2 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>83A</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3423</ID>
              <Description>"STEALS"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>4080FF</Color>
              <VariableType>2 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>850</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3424</ID>
              <Description>"BLOCKS"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>808000</Color>
              <VariableType>2 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>852</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3425</ID>
              <Description>"ASSISTS"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>4080FF</Color>
              <VariableType>2 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>858</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3426</ID>
              <Description>"FOULS"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>808000</Color>
              <VariableType>2 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>854</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3427</ID>
              <Description>"TURNOVERS"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>4080FF</Color>
              <VariableType>2 Bytes</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>85C</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3428</ID>
              <Description>"SECONDS PLAYED"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>808000</Color>
              <VariableType>Double</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>990</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3429</ID>
              <Description>"CURRENT FATIGUE"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>4080FF</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>E7C</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
            <CheatEntry>
              <ID>3430</ID>
              <Description>"MAX FATIGUE"</Description>
              <LastState Value="??" Activated="0" RealAddress="00000000"/>
              <Color>808000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+1FDA1C0</Address>
              <Offsets>
                <Offset>E80</Offset>
                <Offset>98</Offset>
                <Offset>110</Offset>
                <Offset>D8</Offset>
                <Offset>50</Offset>
              </Offsets>
            </CheatEntry>
          </CheatEntries>
        </CheatEntry>
      </CheatEntries>
    </CheatEntry>
  </CheatEntries>
  <UserdefinedSymbols/>
  <Comments>========== ATTRIBUTES ========== (ADDRESS MAY BE DIFFERENT FOR EACH COMPUTER):
Copy these values (from in game Attribute upgrades section) in this order to a spreadsheet: 
-------------------
(From Inside scorer) 
1.Standing Layup 
2.Driving Layup 
3.Post Fadeway 
4.Post Hook 
5.Post Control
-------------------
(Jump Shooter)
6.Moving Shot Close 
7.Standing Shot Close 
8.Moving Shot MidRange 
9.Standing Sot MidRange 
10.Moving Shot Three 
11.Standing Shot Three 
12.Free Throw
-------------------
Recalculate all values to (Value-25)*3. For example if standing layup is 90 the real value is 195; (90-25)=65*3= 195. Value of 99 would be 222. Copy these 12 values as an array, then search for an Array of byte using cheat engine, making sure to un-check the Hex option. Add the bottom result to your table then change the value to 222 222 222 222 222 222 222 222 222 222 222 222. Check in game to see if your stats went to 99. (My Career &gt; My Minutes &gt; Player card(won't show on upgrades screen)). 
-------------------
If it didn't change undo the changes (ctrl+z) then try another value from your array search. If none work or you don't get any search results start from the beginning and double check your array to ensure it's in the order listed and double check your calculations.
-------------------
If it did change take note of the address that worked. Go to the attributes table and right click Standing Layup &gt; Recalculate new address. Choose the second tab "Change to address" and it should be auto populated with the last address you added to your table, but if it's not, type it in manually. Click change and all of your attributes should update to match your in-game attributes (att-25)*3. You can then highlight all of them, right click then Change record &gt; Value then change them all to 222 to max out.
-------------------
========== BADGES ==========
-------------------
ALL 77 BADGES: FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF 
NO CONFLICTNG: FA FF D5 F2 FF FF FF FF FF FF FF FF FF FF FF 7F 
To find your badges address you'll want to have the game windowed in about 1/2 of your screen with CE open next to it so you can watch them both at the same time. First create a new PG in game and proceed to the point where you can upgrade attributes. Search for an Array of Byte in CE with this address: 78 87 0 0 0 72 72 72 72 69 69 78. Scroll down to the bottom of the list, make sure you can see the full values. Upgrade your Inside Scorer attribute in the game and watch the 2 values that change in CE. Double click the bottom one to add it to your list. Right click the Address and add +88 to the end of it (nba2k15.exe+??????+88). Change the length to 16 characters and click OK. That will be your address for your badges, you can set it to the above value to get 70 badges, or if you want all 77 set it to all FFs.</Comments>
</CheatTable>
