<?xml version="1.0" encoding="utf-8"?>
<CheatTable CheatEngineTableVersion="42">
  <CheatEntries>
    <CheatEntry>
      <ID>0</ID>
      <Description>"ActivateFirst"</Description>
      <Options moHideChildren="1" moActivateChildrenAsWell="1" moDeactivateChildrenAsWell="1"/>
      <VariableType>Auto Assembler Script</VariableType>
      <AssemblerScript>{ Game   : NBA2K20.exe
  Version:
  Date   : 2019-09-07
  Author : modstar

  This script does blah blah blah
}

[ENABLE]

aobscanmodule(INJECT2,NBA2K23.exe,8B 41 0C FF C8 83 F8 01 77) // should be unique
alloc(newmem,$1000,"NBA2K23.exe"+44f269+16fb0) //7FF6CD492749 7FF6CD47B839 16F10
alloc(cqpd,256)
registersymbol(cqpd)

label(code)
label(return)

newmem:

code:
  mov eax,[rcx+0C]
  mov [cqpd],eax
  dec eax
  jmp return

INJECT2:
  jmp newmem
return:
registersymbol(INJECT2)

[DISABLE]

INJECT2:
  db 8B 41 0C FF C8

unregistersymbol(INJECT2)
dealloc(newmem)

{
// ORIGINAL CODE - INJECTION POINT: "NBA2K20.exe"+9BB839

"NBA2K20.exe"+9BB804: 48 8B 0D 6D 98 AE 05  -  mov rcx,[NBA2K20.exe+64A5078]
"NBA2K20.exe"+9BB80B: 48 8B 01              -  mov rax,[rcx]
"NBA2K20.exe"+9BB80E: FF 90 A8 00 00 00     -  call qword ptr [rax+000000A8]
"NBA2K20.exe"+9BB814: 83 F8 04              -  cmp eax,04
"NBA2K20.exe"+9BB817: 76 3D                 -  jna NBA2K20.exe+9BB856
"NBA2K20.exe"+9BB819: 48 63 05 0C 1D F2 03  -  movsxd  rax,dword ptr [NBA2K20.exe+48DD52C]
"NBA2K20.exe"+9BB820: 48 8D 0D A9 A4 F0 03  -  lea rcx,[NBA2K20.exe+48C5CD0]
"NBA2K20.exe"+9BB827: 48 69 C0 D0 00 00 00  -  imul rax,rax000000D0
"NBA2K20.exe"+9BB82E: 48 8B 04 08           -  mov rax,[rax+rcx]
"NBA2K20.exe"+9BB832: 48 8B 88 88 09 00 00  -  mov rcx,[rax+00000988]
// ---------- INJECTING HERE ----------
"NBA2K20.exe"+9BB839: 8B 41 0C              -  mov eax,[rcx+0C]
"NBA2K20.exe"+9BB83C: FF C8                 -  dec eax
// ---------- DONE INJECTING  ----------
"NBA2K20.exe"+9BB83E: 83 F8 01              -  cmp eax,01
"NBA2K20.exe"+9BB841: 77 13                 -  ja NBA2K20.exe+9BB856
"NBA2K20.exe"+9BB843: E8 88 A9 FF FF        -  call NBA2K20.exe+9B61D0
"NBA2K20.exe"+9BB848: 85 C0                 -  test eax,eax
"NBA2K20.exe"+9BB84A: 74 0A                 -  je NBA2K20.exe+9BB856
"NBA2K20.exe"+9BB84C: B8 01 00 00 00        -  mov eax,00000001
"NBA2K20.exe"+9BB851: 48 83 C4 28           -  add rsp,28
"NBA2K20.exe"+9BB855: C3                    -  ret
"NBA2K20.exe"+9BB856: 33 C0                 -  xor eax,eax
"NBA2K20.exe"+9BB858: 48 83 C4 28           -  add rsp,28


}
</AssemblerScript>
      <CheatEntries>
        <CheatEntry>
          <ID>1</ID>
          <Description>"ActivateSecond"</Description>
          <VariableType>Auto Assembler Script</VariableType>
          <AssemblerScript>{ Game   : NBA2K23.exe
  Version:
  Date   : 2022-09-11
  Author : admin

  This script does blah blah blah
}

[ENABLE]

aobscanmodule(INJECT,NBA2K23.exe,89 B0 3C 02 00 00 48) // should be unique
alloc(newmem,$1000,INJECT,"NBA2K23.exe"+1a277a9+1d5d3)

label(code)
label(return)
label(dengyu)

newmem:

code:
  cmp [cqpd],2
  je dengyu
  mov [rax+0000023C],esi
  jmp return

dengyu:
  mov esi,1
  mov [rax+0000023C],esi
  jmp return


INJECT:
  jmp newmem
  nop
return:
registersymbol(INJECT)

[DISABLE]

INJECT:
  db 89 B0 3C 02 00 00

unregistersymbol(INJECT)
dealloc(newmem)

{
// ORIGINAL CODE - INJECTION POINT: INJECT

NBA2K23.exe+1B43BBF: E9 EF 00 00 00        - jmp NBA2K23.exe+1B43CB3
NBA2K23.exe+1B43BC4: 44 8B 4E 44           - mov r9d,[rsi+44]
NBA2K23.exe+1B43BC8: 4C 8D 46 30           - lea r8,[rsi+30]
NBA2K23.exe+1B43BCC: 0F 28 CE              - movaps xmm1,xmm6
NBA2K23.exe+1B43BCF: 48 8B CF              - mov rcx,rdi
NBA2K23.exe+1B43BD2: E8 49 05 FC FF        - call NBA2K23.exe+1B04120
NBA2K23.exe+1B43BD7: 48 8B 87 90 09 00 00  - mov rax,[rdi+00000990]
NBA2K23.exe+1B43BDE: 8B F3                 - mov esi,ebx
NBA2K23.exe+1B43BE0: 0F 2F F0              - comiss xmm6,xmm0
NBA2K23.exe+1B43BE3: 40 0F 97 C6           - seta sil
// ---------- INJECTING HERE ----------
INJECT: 89 B0 3C 02 00 00     - mov [rax+0000023C],esi
// ---------- DONE INJECTING  ----------
NBA2K23.exe+1B43BED: 48 8B 05 24 9B 6F 05  - mov rax,[NBA2K23.exe+723D718]
NBA2K23.exe+1B43BF4: 40 F6 C5 02           - test bpl,02
NBA2K23.exe+1B43BF8: 75 31                 - jne NBA2K23.exe+1B43C2B
NBA2K23.exe+1B43BFA: 48 85 C0              - test rax,rax
NBA2K23.exe+1B43BFD: 74 4F                 - je NBA2K23.exe+1B43C4E
NBA2K23.exe+1B43BFF: 48 8B 88 A8 03 00 00  - mov rcx,[rax+000003A8]
NBA2K23.exe+1B43C06: 48 85 C9              - test rcx,rcx
NBA2K23.exe+1B43C09: 74 20                 - je NBA2K23.exe+1B43C2B
NBA2K23.exe+1B43C0B: 48 8B 51 40           - mov rdx,[rcx+40]
NBA2K23.exe+1B43C0F: 48 85 D2              - test rdx,rdx
}
</AssemblerScript>
        </CheatEntry>
      </CheatEntries>
    </CheatEntry>
  </CheatEntries>
  <UserdefinedSymbols/>
</CheatTable>
