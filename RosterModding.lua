local lang = {}
lang.EN = {
    "Roster Modding",
    [[Basic support for roster modding. 
  * Disabled roster data overwritten by official roster.
  * Disabled player name checks when saving rosters
  * Reset QuickPlay/2KU matchup(avoid crashes after using modded roster)]],
}
lang.ZH = {
    "名单修改基础支持",
    [[名单修改/mod的基础支持
  * 禁用官方名单数据对名单数据的覆盖.
  * 禁用名单保存时球员姓名的检查和重置.
  * 重置快速比赛/2KU的对阵球队(避免使用名单Mod隐藏球队导致游戏游戏闪退)]],
}
function lang.get(key)
    return lang[Hook.getGuiLang()][key]
end

PluginInfo = {
    name = lang.get(1),
    author = "Looyh",
    version = "V1.0",
    description = lang.get(2),
    requiredHookVersion = {0, 0, 6}
}


local items = {
    rosterOverwritten = {
        markCode = "8B 44 24 70 48 89 5C 24 50 48 89 6C",
        offset = 0x1E,
        original = "01",
        asm = "00"
    },
    nameChecking = {
        markCode = "0F 8E 03 01 00 00 0F 1F 84 00",
        offset = 0,
        asm = "E9 04 01 00 00 90"
    },
    matchupQuickPlay = {
        markCode = "48 8B DD 48 8B F0 48 85",
        offset = 0x12,
        original = "74",
        asm = "EB"
    },
    matchup2KU = {
        markCode = "48 0F 44 DF 48 3B F8 48 0F 45 F8 44",
        offset = 0x12,
        original = "74",
        asm = "EB"
    }
}

function Init()
    
    if not initFlag then
    
        for k, item in pairs(items) do
            local addr = Hook.markCodeScan(item.markCode)
            item.ptr = addr and Pointer(addr + item.offset) or nil
        end

        initFlag = true
    end

    for k, item in pairs(items) do
        if item.ptr then
            item.ptr:writeByteArrayString(item.asm)
        end
    end
   
end

function DeInit() 
    
    for k, item in pairs(items) do
        if item.ptr then
            item.ptr:writeByteArrayString(item.original or item.markCode)
        end
    end
   
end