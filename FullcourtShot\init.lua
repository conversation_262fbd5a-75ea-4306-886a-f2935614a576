-- FullcourtShot Plugin for NBA 2K23
-- init.lua - Plugin entry point

-- Plugin Information (required for plugin registration)
PluginInfo = {
    name = "Fullcourt Shot",
    author = "User",
    version = "V1.0",
    description = "Allows shooting from fullcourt distance with perfect accuracy",
    requiredHookVersion = "V0.0.26"
}

-- Plugin state
local fullcourt = {
    enabled = true,
    key_pressed = false,
    frame_count = 0,
    debug_interval = 300  -- Debug every 5 seconds at 60fps
}

-- Main plugin functionality
function fullcourt.perform_shot()
    print("[FullcourtShot] Attempting fullcourt shot...")
    
    local player = get_user_player()
    if player then
        print("[FullcourtShot] Player object obtained")
        
        -- Move player to opposite baseline
        player:set_pos(0.0, 94.0, 0.0)
        print("[FullcourtShot] Player moved to fullcourt position")
        
        -- Perform perfect shot
        local result = player:shoot(true)
        print("[FullcourtShot] Shot executed, result: " .. tostring(result))
        
        return true
    else
        print("[FullcourtShot] ERROR: Could not get player object")
        return false
    end
end

-- Frame update function
function fullcourt.on_frame()
    if not fullcourt.enabled then
        return
    end
    
    fullcourt.frame_count = fullcourt.frame_count + 1
    
    -- Debug output to confirm function is being called
    if fullcourt.frame_count % fullcourt.debug_interval == 0 then
        print("[FullcourtShot] Frame update working - frame " .. fullcourt.frame_count)
    end
    
    -- Check for F1 key press
    if is_key_pressed(0x70) then  -- F1 key
        if not fullcourt.key_pressed then
            fullcourt.key_pressed = true
            print("[FullcourtShot] F1 key detected!")
            fullcourt.perform_shot()
        end
    else
        fullcourt.key_pressed = false
    end
end

-- Plugin lifecycle functions
function Init()
    print("[FullcourtShot] Plugin initialized successfully!")
    print("[FullcourtShot] Press F1 in-game to trigger fullcourt shot")
    return true
end

function DeInit()
    print("[FullcourtShot] Plugin deinitialized")
    return true
end

-- Hook into the frame update system
function OnDrawMenuItem()
    -- This might be called for GUI, but we'll use it to ensure our frame function runs
    fullcourt.on_frame()
end

-- Alternative: Try to hook into the tick system
if Hook and Hook.registerCallback then
    Hook.registerCallback("Tick", fullcourt.on_frame)
end

print("[FullcourtShot] Plugin loaded - " .. PluginInfo.name .. " " .. PluginInfo.version)
